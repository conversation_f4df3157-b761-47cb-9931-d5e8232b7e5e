//+------------------------------------------------------------------+
//|                                           V001_Moving_Average.mq5 |
//|                                  Copyright 2023, Your Name Here   |
//|                                             https://www.yourwebsite.com |
//+------------------------------------------------------------------+
#property copyright "Your Name Here"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 1
#property indicator_plots   1

//--- plot MA
#property indicator_label1  "Moving Average"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

//--- input parameters
input int      InpMAPeriod=14;         // MA Period
input ENUM_MA_METHOD InpMAMethod=MODE_SMA; // MA Method
input ENUM_APPLIED_PRICE InpAppliedPrice=PRICE_CLOSE; // Applied Price

//--- indicator buffers
double         MABuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   //--- indicator buffers mapping
   SetIndexBuffer(0,MABuffer,INDICATOR_DATA);
   
   //--- set accuracy
   IndicatorSetInteger(INDICATOR_DIGITS,_Digits);
   
   //--- set indicator name
   string short_name=StringFormat("MA(%d)",InpMAPeriod);
   IndicatorSetString(INDICATOR_SHORTNAME,short_name);
   
   //--- set first bar from which index will be drawn
   PlotIndexSetInteger(0,PLOT_DRAW_BEGIN,InpMAPeriod-1);
   
   return(INIT_SUCCEEDED);
  }

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   //--- check for rates total
   if(rates_total<InpMAPeriod)
      return(0);
      
   //--- calculate MA
   int start;
   if(prev_calculated==0)
      start=InpMAPeriod;
   else
      start=prev_calculated-1;
      
   for(int i=start;i<rates_total;i++)
     {
      MABuffer[i]=iMA(NULL,0,InpMAPeriod,0,InpMAMethod,InpAppliedPrice,i);
     }
     
   //--- return value of prev_calculated for next call
   return(rates_total);
  }