//+------------------------------------------------------------------+
//|                                                          EA.mq5   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Your Name"
#property link      "https://www.yourwebsite.com"
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 5
#property indicator_plots   5

// Fast MA properties - main chart window
#property indicator_label1  "Fast MA"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrLime
#property indicator_width1  1
#property indicator_style1  STYLE_SOLID

// Slow MA properties - main chart window
#property indicator_label2  "Slow MA"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_width2  1
#property indicator_style2  STYLE_SOLID

// RSI properties - separate window
#property indicator_label3  "RSI"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrBlue
#property indicator_width3  1
#property indicator_style3  STYLE_SOLID

// MACD Main properties - separate window
#property indicator_label4  "MACD Main"
#property indicator_type4   DRAW_LINE
#property indicator_color4  clrMagenta
#property indicator_width4  1
#property indicator_style4  STYLE_SOLID

// MACD Signal properties - separate window
#property indicator_label5  "MACD Signal"
#property indicator_type5   DRAW_LINE
#property indicator_color5  clrYellow
#property indicator_width5  1
#property indicator_style5  STYLE_SOLID

// === Inputs ===
input int fastMAPeriod = 12;
input int slowMAPeriod = 26;
input int rsiPeriod = 14;
input double rsiBuyLevel = 50;
input double rsiSellLevel = 50;
input int macdFast = 12;
input int macdSlow = 26;
input int macdSignal = 9;
input double rewardToRiskRatio = 3.0;
input int swingLookback = 50;

// === Buffers ===
double fastMABuffer[];
double slowMABuffer[];
double rsiBuffer[];
double macdMainBuffer[];
double macdSignalBuffer[];

// Working arrays
double fastMA[];
double slowMA[];
double rsiVal[];
double macdMain[];
double macdSignalLine[];

datetime lastAlertTime = 0;

// === Handles ===
int fastMAHandle;
int slowMAHandle;
int rsiHandle;
int macdHandle;

int OnInit() {
   // Map indicator buffers
   SetIndexBuffer(0, fastMABuffer, INDICATOR_DATA);
   SetIndexBuffer(1, slowMABuffer, INDICATOR_DATA);
   SetIndexBuffer(2, rsiBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, macdMainBuffer, INDICATOR_DATA);
   SetIndexBuffer(4, macdSignalBuffer, INDICATOR_DATA);
   
   // Set indicator properties
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   IndicatorSetString(INDICATOR_SHORTNAME, "Multi-Indicator System");
   
   // Set separate window for RSI
   PlotIndexSetInteger(2, PLOT_DRAW_BEGIN, rsiPeriod);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0.0);
   
   // Set separate window for MACD
   PlotIndexSetInteger(3, PLOT_DRAW_BEGIN, macdSlow);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetInteger(4, PLOT_DRAW_BEGIN, macdSlow);
   PlotIndexSetDouble(4, PLOT_EMPTY_VALUE, 0.0);
   
   // Initialize indicator handles
   fastMAHandle = iMA(_Symbol, _Period, fastMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
   slowMAHandle = iMA(_Symbol, _Period, slowMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
   rsiHandle    = iRSI(_Symbol, _Period, rsiPeriod, PRICE_CLOSE);
   macdHandle   = iMACD(_Symbol, _Period, macdFast, macdSlow, macdSignal, PRICE_CLOSE);
   
   // Check if handles are valid
   if(fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE || 
      rsiHandle == INVALID_HANDLE || macdHandle == INVALID_HANDLE) {
      Print("Error creating indicator handles");
      return INIT_FAILED;
   }

   return INIT_SUCCEEDED;
}

void LogLastHighsLowsAndCurrent() {
   datetime today = TimeCurrent();
   datetime startOfDay = StringToTime(TimeToString(today, TIME_DATE));

   double highs[100], lows[100];
   datetime times[100];
   int count = 0;

   // ✅ Fixed the loop (line 63 was here)
   for (int i = 0; (i < Bars(_Symbol, _Period)) && (count < 100); i++) {
      datetime t = iTime(NULL, 0, i);
      if (t < startOfDay) break;
      highs[count] = iHigh(NULL, 0, i);
      lows[count] = iLow(NULL, 0, i);
      times[count] = t;
      count++;
   }

   if (count >= 5) {
      for (int i = 0; i < 5; i++) {
         int highIdx = ArrayMaximum(highs, count);
         int lowIdx = ArrayMinimum(lows, count);

         Print("High #", i + 1, " -> Time: ", TimeToString(times[highIdx], TIME_DATE | TIME_MINUTES), ", Price: ", DoubleToString(highs[highIdx], _Digits));
         Print("Low  #", i + 1, " -> Time: ", TimeToString(times[lowIdx], TIME_DATE | TIME_MINUTES), ", Price: ", DoubleToString(lows[lowIdx], _Digits));

         highs[highIdx] = -DBL_MAX;
         lows[lowIdx] = DBL_MAX;
      }
   }

   datetime now = TimeCurrent();
   double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   Print("Current -> Time: ", TimeToString(now, TIME_DATE | TIME_MINUTES), ", Price: ", DoubleToString(price, _Digits));
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {

   if (rates_total < 50)
      return 0;

   int shift = 1;

   if (CopyBuffer(fastMAHandle, 0, 0, rates_total, fastMA) <= 0) return 0;
   if (CopyBuffer(slowMAHandle, 0, 0, rates_total, slowMA) <= 0) return 0;
   if (CopyBuffer(rsiHandle, 0, 0, rates_total, rsiVal) <= 0) return 0;
   if (CopyBuffer(macdHandle, 0, 0, rates_total, macdMain) <= 0) return 0;
   if (CopyBuffer(macdHandle, 1, 0, rates_total, macdSignalLine) <= 0) return 0;

   double macdHist = macdMain[shift] - macdSignalLine[shift];
   double price = close[shift];

   for (int i = 0; i < rates_total; i++) {
      fastMABuffer[i] = fastMA[i];
      slowMABuffer[i] = slowMA[i];
   }

   LogLastHighsLowsAndCurrent();

   if (TimeCurrent() == lastAlertTime)
      return rates_total;

   // ✅ BUY SIGNAL
   if (
       (fastMA[shift] > slowMA[shift]) &&
       (rsiVal[shift] < rsiBuyLevel) &&
       (macdHist > 0.0)
   ) {
      double sl = iLow(NULL, 0, iLowest(NULL, 0, MODE_LOW, swingLookback, shift));
      double risk = price - sl;
      if (risk <= 0) return rates_total;
      double tp = price + rewardToRiskRatio * risk;

      string msg = StringFormat("📈 BUY SIGNAL\nEntry: %.5f\nSL: %.5f\nTP: %.5f", price, sl, tp);
      Alert(msg);
      SendNotification(msg);
      lastAlertTime = TimeCurrent();
   }

   // ✅ SELL SIGNAL
   if (
       (fastMA[shift] < slowMA[shift]) &&
       (rsiVal[shift] > rsiSellLevel) &&
       (macdHist < 0.0)
   ) {
      double sl = iHigh(NULL, 0, iHighest(NULL, 0, MODE_HIGH, swingLookback, shift));
      double risk = sl - price;
      if (risk <= 0) return rates_total;
      double tp = price - rewardToRiskRatio * risk;

      string msg = StringFormat("📉 SELL SIGNAL\nEntry: %.5f\nSL: %.5f\nTP: %.5f", price, sl, tp);
      Alert(msg);
      SendNotification(msg);
      lastAlertTime = TimeCurrent();
   }

   return rates_total;
}
