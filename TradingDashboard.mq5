//+------------------------------------------------------------------+
//|                                            TradingDashboard.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 3
#property indicator_plots   3

// Fast MA properties
#property indicator_label1  "Fast MA"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrLime
#property indicator_width1  2
#property indicator_style1  STYLE_SOLID

// Slow MA properties
#property indicator_label2  "Slow MA"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_width2  2
#property indicator_style2  STYLE_SOLID

// Buy/Sell Signals
#property indicator_label3  "Signals"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrYellow
#property indicator_width3  3
#property indicator_style3  STYLE_SOLID

// === Inputs ===
input int fastMAPeriod = 12;
input int slowMAPeriod = 26;
input int rsiPeriod = 14;
input double rsiBuyLevel = 50;
input double rsiSellLevel = 50;
input int macdFast = 12;
input int macdSlow = 26;
input int macdSignal = 9;
input double rewardToRiskRatio = 3.0;
input int swingLookback = 50;

// === Buffers ===
double fastMABuffer[];
double slowMABuffer[];
double signalBuffer[];

// Working arrays
double fastMA[];
double slowMA[];
double rsiVal[];
double macdMain[];
double macdSignalLine[];

datetime lastAlertTime = 0;

// === Handles ===
int fastMAHandle;
int slowMAHandle;
int rsiHandle;
int macdHandle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit() {
   // Map indicator buffers
   SetIndexBuffer(0, fastMABuffer, INDICATOR_DATA);
   SetIndexBuffer(1, slowMABuffer, INDICATOR_DATA);
   SetIndexBuffer(2, signalBuffer, INDICATOR_DATA);
   
   // Set up signal arrows
   PlotIndexSetInteger(2, PLOT_ARROW, 159); // Circle
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   
   // Set indicator labels
   PlotIndexSetString(0, PLOT_LABEL, "Fast MA(" + IntegerToString(fastMAPeriod) + ")");
   PlotIndexSetString(1, PLOT_LABEL, "Slow MA(" + IntegerToString(slowMAPeriod) + ")");
   PlotIndexSetString(2, PLOT_LABEL, "Trading Signals");
   
   // Create indicator handles
   fastMAHandle = iMA(_Symbol, _Period, fastMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
   slowMAHandle = iMA(_Symbol, _Period, slowMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
   rsiHandle = iRSI(_Symbol, _Period, rsiPeriod, PRICE_CLOSE);
   macdHandle = iMACD(_Symbol, _Period, macdFast, macdSlow, macdSignal, PRICE_CLOSE);
   
   if (fastMAHandle == INVALID_HANDLE || slowMAHandle == INVALID_HANDLE || 
       rsiHandle == INVALID_HANDLE || macdHandle == INVALID_HANDLE) {
      Print("Failed to create indicator handles");
      return INIT_FAILED;
   }
   
   // Resize working arrays
   ArrayResize(fastMA, 100);
   ArrayResize(slowMA, 100);
   ArrayResize(rsiVal, 100);
   ArrayResize(macdMain, 100);
   ArrayResize(macdSignalLine, 100);
   
   // Create info panel
   CreateInfoPanel();
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Create information panel                                         |
//+------------------------------------------------------------------+
void CreateInfoPanel() {
   // Create background rectangle
   ObjectCreate(0, "InfoPanel", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_YDISTANCE, 30);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_XSIZE, 250);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_YSIZE, 150);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, "InfoPanel", OBJPROP_WIDTH, 1);
   
   // Create text labels
   string labels[] = {"RSI_Label", "MACD_Label", "Signal_Label", "MA_Label"};
   int yPos[] = {50, 70, 90, 110};
   
   for (int i = 0; i < ArraySize(labels); i++) {
      ObjectCreate(0, labels[i], OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, labels[i], OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(0, labels[i], OBJPROP_XDISTANCE, 15);
      ObjectSetInteger(0, labels[i], OBJPROP_YDISTANCE, yPos[i]);
      ObjectSetInteger(0, labels[i], OBJPROP_COLOR, clrWhite);
      ObjectSetInteger(0, labels[i], OBJPROP_FONTSIZE, 9);
      ObjectSetString(0, labels[i], OBJPROP_FONT, "Arial");
   }
}

//+------------------------------------------------------------------+
//| Update information panel                                         |
//+------------------------------------------------------------------+
void UpdateInfoPanel() {
   if (ArraySize(rsiVal) > 1 && ArraySize(macdMain) > 1) {
      double currentRSI = rsiVal[1];
      double currentMACD = macdMain[1] - macdSignalLine[1];
      
      string rsiText = "RSI: " + DoubleToString(currentRSI, 2);
      string macdText = "MACD: " + DoubleToString(currentMACD, 5);
      string maText = "MA Trend: " + (fastMA[1] > slowMA[1] ? "BULLISH" : "BEARISH");
      
      string signalText = "Signal: ";
      if ((fastMA[1] > slowMA[1]) && (currentRSI < rsiBuyLevel) && (currentMACD > 0.0)) {
         signalText += "BUY";
         ObjectSetInteger(0, "Signal_Label", OBJPROP_COLOR, clrLime);
      } else if ((fastMA[1] < slowMA[1]) && (currentRSI > rsiSellLevel) && (currentMACD < 0.0)) {
         signalText += "SELL";
         ObjectSetInteger(0, "Signal_Label", OBJPROP_COLOR, clrRed);
      } else {
         signalText += "WAIT";
         ObjectSetInteger(0, "Signal_Label", OBJPROP_COLOR, clrYellow);
      }
      
      ObjectSetString(0, "RSI_Label", OBJPROP_TEXT, rsiText);
      ObjectSetString(0, "MACD_Label", OBJPROP_TEXT, macdText);
      ObjectSetString(0, "Signal_Label", OBJPROP_TEXT, signalText);
      ObjectSetString(0, "MA_Label", OBJPROP_TEXT, maText);
   }
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   // Release handles
   if (fastMAHandle != INVALID_HANDLE) IndicatorRelease(fastMAHandle);
   if (slowMAHandle != INVALID_HANDLE) IndicatorRelease(slowMAHandle);
   if (rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if (macdHandle != INVALID_HANDLE) IndicatorRelease(macdHandle);
   
   // Remove objects
   ObjectDelete(0, "InfoPanel");
   ObjectDelete(0, "RSI_Label");
   ObjectDelete(0, "MACD_Label");
   ObjectDelete(0, "Signal_Label");
   ObjectDelete(0, "MA_Label");
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
   
   if (rates_total < 50)
      return 0;
   
   int shift = 1;
   
   // Copy indicator data
   if (CopyBuffer(fastMAHandle, 0, 0, rates_total, fastMA) <= 0) return 0;
   if (CopyBuffer(slowMAHandle, 0, 0, rates_total, slowMA) <= 0) return 0;
   if (CopyBuffer(rsiHandle, 0, 0, rates_total, rsiVal) <= 0) return 0;
   if (CopyBuffer(macdHandle, 0, 0, rates_total, macdMain) <= 0) return 0;
   if (CopyBuffer(macdHandle, 1, 0, rates_total, macdSignalLine) <= 0) return 0;
   
   double macdHist = macdMain[shift] - macdSignalLine[shift];
   double price = close[shift];
   
   // Copy to display buffers
   for (int i = 0; i < rates_total; i++) {
      fastMABuffer[i] = fastMA[i];
      slowMABuffer[i] = slowMA[i];
      signalBuffer[i] = EMPTY_VALUE;
   }
   
   // Update info panel
   UpdateInfoPanel();
   
   if (TimeCurrent() == lastAlertTime)
      return rates_total;
   
   // Check for signals
   if ((fastMA[shift] > slowMA[shift]) && (rsiVal[shift] < rsiBuyLevel) && (macdHist > 0.0)) {
      signalBuffer[shift] = low[shift] - (10 * _Point);
      PlotIndexSetInteger(2, PLOT_ARROW, 233); // Up arrow
      PlotIndexSetInteger(2, PLOT_COLOR, clrLime);
      
      double sl = iLow(NULL, 0, iLowest(NULL, 0, MODE_LOW, swingLookback, shift));
      double risk = price - sl;
      if (risk > 0) {
         double tp = price + rewardToRiskRatio * risk;
         string msg = StringFormat("📈 BUY SIGNAL\nEntry: %.5f\nSL: %.5f\nTP: %.5f", price, sl, tp);
         Alert(msg);
         lastAlertTime = TimeCurrent();
      }
   }
   else if ((fastMA[shift] < slowMA[shift]) && (rsiVal[shift] > rsiSellLevel) && (macdHist < 0.0)) {
      signalBuffer[shift] = high[shift] + (10 * _Point);
      PlotIndexSetInteger(2, PLOT_ARROW, 234); // Down arrow
      PlotIndexSetInteger(2, PLOT_COLOR, clrRed);
      
      double sl = iHigh(NULL, 0, iHighest(NULL, 0, MODE_HIGH, swingLookback, shift));
      double risk = sl - price;
      if (risk > 0) {
         double tp = price - rewardToRiskRatio * risk;
         string msg = StringFormat("📉 SELL SIGNAL\nEntry: %.5f\nSL: %.5f\nTP: %.5f", price, sl, tp);
         Alert(msg);
         lastAlertTime = TimeCurrent();
      }
   }
   
   return rates_total;
}
