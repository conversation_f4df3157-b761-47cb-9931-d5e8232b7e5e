# Access Database Creation - Step by Step

## 🎯 **Easiest Method: Use Access Table Designer**

Since Access SQL has limitations, here's the easiest way to create your tables:

### **Step 1: Create Tables Using Design View**

#### **Table 1: Employees**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| EmployeeID | AutoNumber | Long Integer | Yes | |
| RegisterName | Short Text | 50 | Yes | |
| RealName | Short Text | 50 | Yes | |
| Surname | Short Text | 50 | Yes | |
| EmployeeNumber | Short Text | 20 | Yes | |
| IDPassport | Short Text | 20 | Yes | |
| Photo | Attachment | | No | |
| BankName | Short Text | 50 | No | |
| BankBranchName | Short Text | 50 | No | |
| BankAccountNumber | Short Text | 20 | No | |
| BankBranchNumber | Short Text | 10 | No | |
| PhoneNumber | Short Text | 15 | No | |
| EmployeeGroup | Short Text | 30 | No | |
| IsActive | Yes/No | | No | True |
| TerminationDate | Date/Time | | No | |
| CreditLimit | Currency | | No | 1000 |
| CurrentCredit | Currency | | No | 0 |
| DateCreated | Date/Time | | No | Now() |
| DateModified | Date/Time | | No | Now() |

3. **Set Primary Key**: Right-click EmployeeID → Primary Key
4. **Save as**: Employees

#### **Table 2: Attendance**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| AttendanceID | AutoNumber | Long Integer | Yes | |
| EmployeeID | Number | Long Integer | Yes | |
| AttendanceDate | Date/Time | | Yes | |
| CheckInTime | Date/Time | | No | |
| CheckOutTime | Date/Time | | No | |
| HoursWorked | Number | Double | No | 0 |
| IsPresent | Yes/No | | No | False |
| Notes | Long Text | | No | |
| DateCreated | Date/Time | | No | Now() |

3. **Set Primary Key**: AttendanceID
4. **Save as**: Attendance

#### **Table 3: Products**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| ProductID | AutoNumber | Long Integer | Yes | |
| ProductCode | Short Text | 20 | Yes | |
| ProductName | Short Text | 100 | Yes | |
| ProductDescription | Long Text | | No | |
| CurrentStock | Number | Double | No | 0 |
| WeightedAverageCost | Currency | | No | 0 |
| MarkupPercentage | Number | Double | No | 0 |
| SellingPrice | Currency | | No | 0 |
| MinimumStock | Number | Double | No | 0 |
| IsActive | Yes/No | | No | True |
| DateCreated | Date/Time | | No | Now() |
| DateModified | Date/Time | | No | Now() |

3. **Set Primary Key**: ProductID
4. **Save as**: Products

#### **Table 4: StockMovements**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| MovementID | AutoNumber | Long Integer | Yes | |
| ProductID | Number | Long Integer | Yes | |
| MovementType | Short Text | 10 | Yes | |
| Quantity | Number | Double | Yes | |
| UnitCost | Currency | | No | |
| TotalCost | Currency | | No | |
| MovementDate | Date/Time | | No | Now() |
| Reference | Short Text | 50 | No | |
| Notes | Long Text | | No | |

3. **Set Primary Key**: MovementID
4. **Save as**: StockMovements

#### **Table 5: Transactions**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| TransactionID | AutoNumber | Long Integer | Yes | |
| EmployeeID | Number | Long Integer | Yes | |
| TransactionDate | Date/Time | | No | Now() |
| TotalAmount | Currency | | No | 0 |
| TransactionStatus | Short Text | 20 | No | Pending |
| PaymentMethod | Short Text | 20 | No | Credit |
| Notes | Long Text | | No | |

3. **Set Primary Key**: TransactionID
4. **Save as**: Transactions

#### **Table 6: TransactionDetails**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| DetailID | AutoNumber | Long Integer | Yes | |
| TransactionID | Number | Long Integer | Yes | |
| ProductID | Number | Long Integer | Yes | |
| Quantity | Number | Double | Yes | |
| UnitPrice | Currency | | Yes | |
| TotalPrice | Currency | | Yes | |

3. **Set Primary Key**: DetailID
4. **Save as**: TransactionDetails

#### **Table 7: SystemSettings**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| SettingID | AutoNumber | Long Integer | Yes | |
| SettingName | Short Text | 50 | Yes | |
| SettingValue | Short Text | 255 | No | |
| SettingDescription | Long Text | | No | |
| DateModified | Date/Time | | No | Now() |

3. **Set Primary Key**: SettingID
4. **Save as**: SystemSettings

#### **Table 8: EmployeeGroups**
1. **Create → Table Design**
2. **Add these fields**:

| Field Name | Data Type | Field Size | Required | Default Value |
|------------|-----------|------------|----------|---------------|
| GroupID | AutoNumber | Long Integer | Yes | |
| GroupName | Short Text | 30 | Yes | |
| GroupDescription | Long Text | | No | |
| DefaultCreditLimit | Currency | | No | 1000 |
| IsActive | Yes/No | | No | True |

3. **Set Primary Key**: GroupID
4. **Save as**: EmployeeGroups

### **Step 2: Create Relationships**
1. **Database Tools → Relationships**
2. **Add all tables**
3. **Create these relationships**:
   - Employees.EmployeeID → Attendance.EmployeeID (One-to-Many)
   - Employees.EmployeeID → Transactions.EmployeeID (One-to-Many)
   - Products.ProductID → StockMovements.ProductID (One-to-Many)
   - Products.ProductID → TransactionDetails.ProductID (One-to-Many)
   - Transactions.TransactionID → TransactionDetails.TransactionID (One-to-Many)

### **Step 3: Add Sample Data**
1. **Open SystemSettings table**
2. **Add these records**:

| SettingName | SettingValue | SettingDescription |
|-------------|--------------|-------------------|
| DefaultCreditLimit | 1000 | Default credit limit for new employees |
| DefaultMarkupPercentage | 25 | Default markup percentage for products |
| WorkingHoursPerDay | 8 | Standard working hours per day |
| CompanyName | Your Company Name | Company name for reports |
| CompanyAddress | Your Company Address | Company address for reports |

3. **Open EmployeeGroups table**
4. **Add these records**:

| GroupName | GroupDescription | DefaultCreditLimit |
|-----------|------------------|-------------------|
| Management | Management level employees | 2000 |
| Supervisors | Supervisor level employees | 1500 |
| General Staff | General staff members | 1000 |
| Temporary | Temporary employees | 500 |

## 🔧 **Alternative: Import from Excel**

If you prefer, I can create Excel templates that you can import:

### **Option A: Excel Import Method**
1. Create Excel files with the table structures
2. Use Access "External Data → Excel" to import
3. Set up relationships after import

### **Option B: Access Template Method**
1. Use Access built-in templates as starting point
2. Modify tables to match our requirements
3. Add custom fields and relationships

## 📋 **Next Steps After Tables Are Created**

1. **Add VBA Modules** (Copy code from .bas files)
2. **Create Forms** (Use Form Wizard, then customize)
3. **Create Reports** (Use Report Wizard, then customize)
4. **Test System** (Add sample data and test functionality)

## 🆘 **Need Help?**

If you get stuck at any point:
1. **Take screenshots** of any error messages
2. **Note which step** you're having trouble with
3. **Check Access version** (2016, 2019, or 365)

The table creation is the foundation - once this is done, the rest becomes much easier!

Would you like me to create Excel templates for easier import, or would you prefer to continue with the manual table creation method?
