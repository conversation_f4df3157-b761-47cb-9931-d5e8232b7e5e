//+------------------------------------------------------------------+
//|                                                  MACD_Custom.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 3
#property indicator_plots   3

// MACD Main Line
#property indicator_label1  "MACD"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_width1  2
#property indicator_style1  STYLE_SOLID

// MACD Signal Line
#property indicator_label2  "Signal"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_width2  1
#property indicator_style2  STYLE_SOLID

// MACD Histogram
#property indicator_label3  "Histogram"
#property indicator_type3   DRAW_HISTOGRAM
#property indicator_color3  clrGray
#property indicator_width3  2
#property indicator_style3  STYLE_SOLID

// Zero level
#property indicator_level1 0
#property indicator_levelcolor clrGray
#property indicator_levelstyle STYLE_DOT

// Input parameters
input int macdFast = 12;
input int macdSlow = 26;
input int macdSignal = 9;

// Indicator buffers
double macdMainBuffer[];
double macdSignalBuffer[];
double macdHistBuffer[];

// Indicator handle
int macdHandle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit() {
   // Set indicator buffers
   SetIndexBuffer(0, macdMainBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, macdSignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, macdHistBuffer, INDICATOR_DATA);
   
   // Create MACD handle
   macdHandle = iMACD(_Symbol, _Period, macdFast, macdSlow, macdSignal, PRICE_CLOSE);
   if (macdHandle == INVALID_HANDLE) {
      Print("Failed to create MACD handle");
      return INIT_FAILED;
   }
   
   // Set indicator properties
   IndicatorSetString(INDICATOR_SHORTNAME, "MACD(" + IntegerToString(macdFast) + "," + IntegerToString(macdSlow) + "," + IntegerToString(macdSignal) + ")");
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits + 1);
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   if (macdHandle != INVALID_HANDLE)
      IndicatorRelease(macdHandle);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
   
   if (rates_total < macdSlow)
      return 0;
   
   // Copy MACD values to buffers
   if (CopyBuffer(macdHandle, 0, 0, rates_total, macdMainBuffer) <= 0)
      return 0;
   if (CopyBuffer(macdHandle, 1, 0, rates_total, macdSignalBuffer) <= 0)
      return 0;
   
   // Calculate histogram
   for (int i = 0; i < rates_total; i++) {
      macdHistBuffer[i] = macdMainBuffer[i] - macdSignalBuffer[i];
   }
   
   return rates_total;
}
