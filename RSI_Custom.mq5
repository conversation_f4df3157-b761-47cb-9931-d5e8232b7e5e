//+------------------------------------------------------------------+
//|                                                   RSI_Custom.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 1
#property indicator_plots   1

// RSI properties
#property indicator_label1  "RSI"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_width1  2
#property indicator_style1  STYLE_SOLID

// RSI levels
#property indicator_level1 30
#property indicator_level2 50
#property indicator_level3 70
#property indicator_levelcolor clrGray
#property indicator_levelstyle STYLE_DOT

// Input parameters
input int rsiPeriod = 14;

// Indicator buffer
double rsiBuffer[];

// Indicator handle
int rsiHandle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit() {
   // Set indicator buffer
   SetIndexBuffer(0, rsiBuffer, INDICATOR_DATA);
   
   // Create RSI handle
   rsiHandle = iRSI(_Symbol, _Period, rsiPeriod, PRICE_CLOSE);
   if (rsiHandle == INVALID_HANDLE) {
      Print("Failed to create RSI handle");
      return INIT_FAILED;
   }
   
   // Set indicator properties
   IndicatorSetString(INDICATOR_SHORTNAME, "RSI(" + IntegerToString(rsiPeriod) + ")");
   IndicatorSetInteger(INDICATOR_DIGITS, 2);
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   if (rsiHandle != INVALID_HANDLE)
      IndicatorRelease(rsiHandle);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
   
   if (rates_total < rsiPeriod)
      return 0;
   
   // Copy RSI values to buffer
   if (CopyBuffer(rsiHandle, 0, 0, rates_total, rsiBuffer) <= 0)
      return 0;
   
   return rates_total;
}
