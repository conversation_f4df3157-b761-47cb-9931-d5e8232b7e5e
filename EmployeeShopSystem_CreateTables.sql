-- Employee Shop Management System - Database Structure
-- This SQL script creates all necessary tables for the Access database

-- 1. EMPLOYEES TABLE
CREATE TABLE Employees (
    Employee<PERSON> AUTOINCREMENT PRIMARY KEY,
    RegisterName TEXT(50) NOT NULL,
    RealName TEXT(50) NOT NULL,
    Surname TEXT(50) NOT NULL,
    EmployeeNumber TEXT(20) UNIQUE NOT NULL,
    IDPassport TEXT(20) UNIQUE NOT NULL,
    Photo OLEOBJECT,
    BankName TEXT(50),
    BankBranchName TEXT(50),
    BankAccountNumber TEXT(20),
    BankBranchNumber TEXT(10),
    PhoneNumber TEXT(15),
    EmployeeGroup TEXT(30),
    IsActive YESNO DEFAULT True,
    TerminationDate DATETIME,
    CreditLimit CURRENCY DEFAULT 1000,
    CurrentCredit CURRENCY DEFAULT 0,
    DateCreated DATETIME DEFAULT Now(),
    DateModified DATETIME DEFAULT Now()
);

-- 2. ATTENDANCE TABLE
CREATE TABLE Attendance (
    AttendanceID AUTOINCREMENT PRIMARY KEY,
    Employee<PERSON> LONG,
    AttendanceDate DATETIME NOT NULL,
    CheckInTime DATETIME,
    CheckOutTime DATETIME,
    HoursWorked DOUBLE DEFAULT 0,
    IsPresent YESNO DEFAULT False,
    Notes TEXT(255),
    DateCreated DATETIME DEFAULT Now(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- 3. PRODUCTS TABLE
CREATE TABLE Products (
    ProductID AUTOINCREMENT PRIMARY KEY,
    ProductCode TEXT(20) UNIQUE NOT NULL,
    ProductName TEXT(100) NOT NULL,
    ProductDescription TEXT(255),
    CurrentStock DOUBLE DEFAULT 0,
    WeightedAverageCost CURRENCY DEFAULT 0,
    MarkupPercentage DOUBLE DEFAULT 0,
    SellingPrice CURRENCY DEFAULT 0,
    MinimumStock DOUBLE DEFAULT 0,
    IsActive YESNO DEFAULT True,
    DateCreated DATETIME DEFAULT Now(),
    DateModified DATETIME DEFAULT Now()
);

-- 4. STOCK MOVEMENTS TABLE
CREATE TABLE StockMovements (
    MovementID AUTOINCREMENT PRIMARY KEY,
    ProductID LONG,
    MovementType TEXT(10) NOT NULL, -- 'IN' or 'OUT'
    Quantity DOUBLE NOT NULL,
    UnitCost CURRENCY,
    TotalCost CURRENCY,
    MovementDate DATETIME DEFAULT Now(),
    Reference TEXT(50),
    Notes TEXT(255),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- 5. TRANSACTIONS TABLE
CREATE TABLE Transactions (
    TransactionID AUTOINCREMENT PRIMARY KEY,
    EmployeeID LONG,
    TransactionDate DATETIME DEFAULT Now(),
    TotalAmount CURRENCY DEFAULT 0,
    TransactionStatus TEXT(20) DEFAULT 'Pending', -- 'Pending', 'Completed', 'Cancelled'
    PaymentMethod TEXT(20) DEFAULT 'Credit', -- 'Credit', 'Cash', 'Card'
    Notes TEXT(255),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- 6. TRANSACTION DETAILS TABLE
CREATE TABLE TransactionDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    TransactionID LONG,
    ProductID LONG,
    Quantity DOUBLE NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    FOREIGN KEY (TransactionID) REFERENCES Transactions(TransactionID),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- 7. SYSTEM SETTINGS TABLE
CREATE TABLE SystemSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingName TEXT(50) UNIQUE NOT NULL,
    SettingValue TEXT(255),
    SettingDescription TEXT(255),
    DateModified DATETIME DEFAULT Now()
);

-- Insert default system settings
INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) VALUES
('DefaultCreditLimit', '1000', 'Default credit limit for new employees'),
('DefaultMarkupPercentage', '25', 'Default markup percentage for products'),
('WorkingHoursPerDay', '8', 'Standard working hours per day'),
('CompanyName', 'Your Company Name', 'Company name for reports'),
('CompanyAddress', 'Your Company Address', 'Company address for reports');

-- 8. EMPLOYEE GROUPS TABLE (for categorizing employees)
CREATE TABLE EmployeeGroups (
    GroupID AUTOINCREMENT PRIMARY KEY,
    GroupName TEXT(30) UNIQUE NOT NULL,
    GroupDescription TEXT(255),
    DefaultCreditLimit CURRENCY DEFAULT 1000,
    IsActive YESNO DEFAULT True
);

-- Insert default employee groups
INSERT INTO EmployeeGroups (GroupName, GroupDescription, DefaultCreditLimit) VALUES
('Management', 'Management level employees', 2000),
('Supervisors', 'Supervisor level employees', 1500),
('General Staff', 'General staff members', 1000),
('Temporary', 'Temporary employees', 500);

-- Create indexes for better performance
CREATE INDEX idx_employees_number ON Employees(EmployeeNumber);
CREATE INDEX idx_employees_active ON Employees(IsActive);
CREATE INDEX idx_attendance_date ON Attendance(AttendanceDate);
CREATE INDEX idx_attendance_employee ON Attendance(EmployeeID);
CREATE INDEX idx_products_code ON Products(ProductCode);
CREATE INDEX idx_transactions_date ON Transactions(TransactionDate);
CREATE INDEX idx_transactions_employee ON Transactions(EmployeeID);
